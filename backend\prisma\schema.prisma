// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum CustomerStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
  BLOCKED
}

enum CustomerType {
  INDIVIDUAL
  BUSINESS
  ENTERPRISE
}

enum AddressType {
  HOME
  WORK
  BILLING
  SHIPPING
  OTHER
}

enum ContactType {
  PRIMARY
  SECONDARY
  EMERGENCY
  BUSINESS
}

enum PreferenceType {
  COMMUNICATION
  NOTIFICATION
  PRIVACY
  MARKETING
  LANGUAGE
  TIMEZONE
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  PASSWORD_CHANGE
  STATUS_CHANGE
  VERIFICATION
}

enum PaymentTokenStatus {
  ACTIVE
  EXPIRED
  REVOKED
  SUSPENDED
}

enum PaymentTokenType {
  CARD
  BANK_ACCOUNT
  DIGITAL_WALLET
  TAP_TO_PAY
}

// Main Customer model
model Customer {
  id                String            @id @default(cuid())
  externalId        String?           @unique // For integration with external systems

  // Basic Information
  firstName         String
  lastName          String
  email             String            @unique
  phone             String?
  dateOfBirth       DateTime?

  // Business Information (for business customers)
  companyName       String?
  taxId             String?
  businessType      String?

  // Status and Type
  status            CustomerStatus    @default(ACTIVE)
  type              CustomerType      @default(INDIVIDUAL)

  // Verification
  isEmailVerified   Boolean           @default(false)
  isPhoneVerified   Boolean           @default(false)
  isKycVerified     Boolean           @default(false)

  // Metadata
  tags              String[]          @default([])
  notes             String?

  // Relationships
  addresses         Address[]
  contacts          Contact[]
  preferences       CustomerPreference[]
  auditLogs         AuditLog[]
  segmentMembers    CustomerSegmentMember[]
  paymentTokens     PaymentToken[]

  // Timestamps
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  lastLoginAt       DateTime?

  // Soft delete
  deletedAt         DateTime?

  @@map("customers")
  @@index([email])
  @@index([status])
  @@index([type])
  @@index([createdAt])
  @@index([companyName])
}

// Address model for customer addresses
model Address {
  id              String      @id @default(cuid())
  customerId      String

  // Address Information
  type            AddressType @default(HOME)
  label           String?     // Custom label like "Home", "Office", etc.

  // Address Fields
  street1         String
  street2         String?
  city            String
  state           String
  postalCode      String
  country         String      @default("US")

  // Coordinates (optional)
  latitude        Float?
  longitude       Float?

  // Flags
  isDefault       Boolean     @default(false)
  isVerified      Boolean     @default(false)

  // Relationships
  customer        Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("addresses")
  @@index([customerId])
  @@index([type])
  @@index([country])
}

// Contact model for additional contact information
model Contact {
  id              String      @id @default(cuid())
  customerId      String

  // Contact Information
  type            ContactType @default(PRIMARY)
  label           String?     // Custom label

  // Contact Details
  firstName       String
  lastName        String
  email           String?
  phone           String?
  relationship    String?     // For emergency contacts

  // Flags
  isDefault       Boolean     @default(false)
  isVerified      Boolean     @default(false)

  // Relationships
  customer        Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("contacts")
  @@index([customerId])
  @@index([type])
  @@index([email])
}

// Customer Preferences model
model CustomerPreference {
  id              String          @id @default(cuid())
  customerId      String

  // Preference Information
  type            PreferenceType
  key             String          // Specific preference key like "email_notifications", "language"
  value           String          // Preference value

  // Metadata
  description     String?
  isActive        Boolean         @default(true)

  // Relationships
  customer        Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("customer_preferences")
  @@unique([customerId, type, key])
  @@index([customerId])
  @@index([type])
}

// Audit Log model for tracking customer activities
model AuditLog {
  id              String          @id @default(cuid())
  customerId      String

  // Audit Information
  action          AuditAction
  entity          String          // What was changed (e.g., "customer", "address", "preference")
  entityId        String?         // ID of the changed entity

  // Change Details
  oldValues       Json?           // Previous values (for updates)
  newValues       Json?           // New values (for creates/updates)

  // Context
  userAgent       String?
  ipAddress       String?
  sessionId       String?

  // Metadata
  description     String?
  metadata        Json?           // Additional context data

  // Relationships
  customer        Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime        @default(now())

  @@map("audit_logs")
  @@index([customerId])
  @@index([action])
  @@index([entity])
  @@index([createdAt])
}

// Customer Segments model for grouping customers
model CustomerSegment {
  id              String          @id @default(cuid())

  // Segment Information
  name            String          @unique
  description     String?

  // Segment Criteria (stored as JSON for flexibility)
  criteria        Json            // Conditions for automatic assignment

  // Flags
  isActive        Boolean         @default(true)
  isAutomatic     Boolean         @default(false) // Auto-assign based on criteria

  // Relationships
  customers       CustomerSegmentMember[]

  // Timestamps
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("customer_segments")
  @@index([name])
  @@index([isActive])
}

// Junction table for customer segment membership
model CustomerSegmentMember {
  id              String          @id @default(cuid())
  customerId      String
  segmentId       String

  // Membership Details
  assignedAt      DateTime        @default(now())
  assignedBy      String?         // User ID who assigned (if manual)
  isActive        Boolean         @default(true)

  // Relationships
  customer        Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade)
  segment         CustomerSegment @relation(fields: [segmentId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("customer_segment_members")
  @@unique([customerId, segmentId])
  @@index([customerId])
  @@index([segmentId])
}

// Payment Token model for storing payment method tokens
model PaymentToken {
  id                String              @id @default(cuid())
  customerId        String

  // Token Information
  tokenHash         String              @unique // Hashed version of the actual token for security
  externalTokenId   String?             // External payment provider token ID (e.g., NearPay token ID)
  paymentProvider   String              // Payment provider (e.g., 'nearpay', 'stripe', 'square')
  tokenType         PaymentTokenType    // Type of payment method
  status            PaymentTokenStatus  @default(ACTIVE)

  // Payment Method Display Info
  maskedInfo        String?             // Masked payment method info for display (e.g., "**** **** **** 1234")
  paymentBrand      String?             // Payment method brand (e.g., "Visa", "Mastercard", "Bank of America")
  expiresAt         DateTime?           // Expiration date for cards

  // Additional metadata from payment provider
  metadata          Json?               // Additional metadata from payment provider
  transactionData   Json?               // Transaction data from NearPay webhook

  // Security and audit fields
  createdByIp       String?             // IP address when token was created
  lastUsedAt        DateTime?           // Last time this token was used
  usageCount        Int                 @default(0) // Number of times token has been used

  // Relationships
  customer          Customer            @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  deletedAt         DateTime?           // Soft delete

  @@map("payment_tokens")
  @@index([customerId, status])
  @@index([tokenHash])
  @@index([externalTokenId])
  @@index([paymentProvider])
  @@index([createdAt])
}
 