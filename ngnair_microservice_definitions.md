## **🧠 NGnair Microservice Definitions (Detailed for AI Implementation)**

### **🔐 1. Auth Service**

**Purpose**:\
Manage platform-wide identity, session management, access tokens, roles,
and SSO. Acts as the trusted source of authentication and authorization
across all services.

**Core Logic**:

-   User authentication (email/password or SSO)

-   Session management (access/refresh tokens)

-   Role and permission resolution

-   Federated identity (SSO binding for third-party apps)

**Access Logic**:

-   Verifies tokens and scopes for all services

-   Issues scoped JWTs and refresh tokens

-   Manages MFA and device sessions

**Use Cases**:

-   Login flow for users

-   SSO login into marketplace apps

-   Token validation for microservice communication

**Entities**:

-   User

-   Role

-   Permission

-   Session

-   OAuthClient

-   FederatedUserLink

**Connected To**:

-   **Accounts** (user-role mappings)

-   **Marketplace** (SSO)

-   **Partners**, **Support**, **Finance**, etc. (token validation)

**Security**:

-   Secure token issuance

-   MFA and session revocation

-   Per-service and per-scope claims in tokens

### **🧾 2. Accounts Service**

**Purpose**:\
Represents business entities on the platform. Manages their users, apps,
customer links, and ties to partners and financial providers.

**Core Logic**:

-   Manages account lifecycles and roles

-   Links users to accounts (multi-tenant)

-   Controls app installations per account

-   Links customers to accounts

**Access Logic**:

-   Scoped by accountId + userId

-   Enforced via user-role (admin, viewer, etc.)

**Use Cases**:

-   Business creates and manages its account

-   Admin adds team members

-   User installs an app or links a customer

**Entities**:

-   Account

-   UserAccountRole

-   AccountAppInstall

-   AccountCustomerLink

**Connected To**:

-   **Auth** (identity validation)

-   **Finance** (provider mapping)

-   **Partners** (account ownership)

-   **Marketplace**, **Support**, **Customers**

**Security**:

-   Multi-account support per user

-   Role enforcement per account

-   Cross-checks partner ownership

### **💸 3. Finance Service**

**Purpose**:\
Manages payments, financial providers, transactions, payouts, disputes,
and token vaults.

**Core Logic**:

-   Routes payments based on provider config

-   Handles refunds, disputes, and settlement tracking

-   Uses provider credentials linked via partner

-   Interfaces with token vault and bank sources

**Access Logic**:

-   Enforces access based on accountId, partner, or app scope

-   Checks transaction permission (finance:write, refund, etc.)

**Use Cases**:

-   App or account initiates a payment

-   Refund is issued via API

-   Partner views residual earnings

**Entities**:

-   Transaction

-   Payout

-   Dispute

-   Provider

-   PartnerProviderCredential

-   CredentialStatus

-   PartnerCredentialVault

-   AccountProviderLink

-   PaymentLink

**Connected To**:

-   **Accounts** (provider/account mapping)

-   **Partners** (credential management)

-   **Marketplace** (CRM-style payment triggers)

-   **OpenBank** (payer bank info)

-   **Customers** (card tokens)

**Security**:

-   PCI-safe token references

-   Vault integration

-   Partner-provider isolation

### **🏦 4. OpenBank Service**

**Purpose**:\
Manages secure connections to external banking systems to pull bank data
(Plaid, Tink, etc.).

**Core Logic**:

-   Links bank accounts via OAuth

-   Pulls bank balances, transactions

-   Associates bank data with either accounts or customers

**Access Logic**:

-   Validates token

-   Verifies scope (openbank:read)

-   Resolves owner (account or customer)

**Use Cases**:

-   Account links its business bank account

-   Customer authorizes bank access during checkout

**Entities**:

-   BankLink

-   BankToken

-   BankTransaction

**Connected To**:

-   **Finance** (payer funding source)

-   **Accounts** (business account linkage)

-   **Customers** (payer bank token)

**Security**:

-   Stores only token references (not full credentials)

-   Consent management per token

### **🧩 5. Marketplace Service**

**Purpose**:\
Hosts and manages third-party integrations. Allows apps to be installed
and used within business accounts, and provides SSO into apps.

**Core Logic**:

-   App publishing and approval

-   App installation per account

-   SSO generation for federated users

-   App permissions enforcement

**Access Logic**:

-   SSO tokens are scoped by accountId + userId

-   Apps can only act within granted scopes

**Use Cases**:

-   CRM app initiates payment

-   POS system creates a refund

-   User logs into app via NGnair

**Entities**:

-   App

-   AppInstall

-   AppSSOLink

**Connected To**:

-   **Auth** (SSO, federation)

-   **Accounts** (installed apps)

-   **Finance** (transaction initiation)

-   **Customers** (checkout integration)

**Security**:

-   Per-app scopes

-   SSO tokens with expiration + limited scope

### **🧑‍🤝‍🧑 6. Partners Service**

**Purpose**:\
Manages partner organizations that bring users/accounts to NGnair.
Tracks recruitment, earnings, and provider selection.

**Core Logic**:

-   Partner selects supported providers

-   Assigns providers to recruited accounts

-   Manages agents, referrals, and earnings

-   Generates referral tracking links

**Access Logic**:

-   Partner users scoped by org

-   Role-based: admin, agent, referral

-   Residuals based on assigned accounts

**Use Cases**:

-   Agent recruits a user → creates an account

-   Referral user generates a referral link

-   Partner assigns provider and sees earnings

**Entities**:

-   PartnerOrg

-   PartnerUser (role: admin, agent, referral)

-   RecruitedAccount

-   ReferralLink

-   ResidualEarnings

-   PartnerProviderAssignment

**Connected To**:

-   **Auth** (user identity)

-   **Finance** (provider + earnings)

-   **Accounts** (recruited accounts)

-   **Support** (partner-level tickets)

**Security**:

-   Cannot view OpenBank or customer PII

-   Scoped view of accounts + apps

### **🎫 7. Support Service**

**Purpose**:\
Provides internal and partner support workflow, with scoped ticket
access per account, category, or partner.

**Core Logic**:

-   Ticket creation and visibility by type

-   Routing by support tier or partner scope

-   Comments, attachments, resolution flows

**Access Logic**:

-   Role- and category-based:

    -   Internal: all technical/support

    -   Partner: only their recruited accounts, and only for allowed
        categories

    -   Account Admin: view all account tickets

**Use Cases**:

-   User files account support ticket

-   Dispute triggers a payment ticket

-   Partner follows up with account admin

**Entities**:

-   SupportTicket

-   TicketCategory

-   TicketComment

**Connected To**:

-   **Accounts** (ticket-account linking)

-   **Partners** (recruiter for scope)

-   **Finance** (transaction context)

-   **Customers** (payer identity)

**Security**:

-   Support scopes and categories enforced

-   Audit logs for escalations

### **👤 8. Customers Service**

**Purpose**:\
Provides centralized identity and token vaulting for customers (payers)
who transact across NGnair accounts.

**Core Logic**:

-   Store reusable customer data (tokens, identity)

-   Map customers to accounts (1-to-many)

-   Enable token re-use across payments

**Access Logic**:

-   Scopes like customer:read, token:read, etc.

-   Token access only via authorized services

**Use Cases**:

-   Customer uses same card across multiple accounts

-   Partner wants export of all tokens linked to an account

-   Checkout needs to prefill customer info

**Entities**:

-   Customer

-   CustomerCardToken

-   CustomerBankToken

-   CustomerAccountLink

**Connected To**:

-   **Accounts** (business link)

-   **Finance** (token vaulting)

-   **Marketplace** (checkout flow)

-   **OpenBank** (bank linkage)

**Security**:

-   All tokens must be abstract references (vaulted)

-   Consent and scope per account + payer
