/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY,
    NEXT_PUBLIC_AUTH_FRONTEND_URL: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL,
    NEXT_PUBLIC_AUTH_JWKS_URL: process.env.NEXT_PUBLIC_AUTH_JWKS_URL,
    NEXT_PUBLIC_CUSTOMER_API_URL: process.env.NEXT_PUBLIC_CUSTOMER_API_URL,
    NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL: process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL,
    NEXT_PUBLIC_AUTH_DEBUG_MODE: process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE,
  },
  images: {
    domains: process.env.NEXT_PUBLIC_IMAGE_DOMAINS ?
      process.env.NEXT_PUBLIC_IMAGE_DOMAINS.split(',') :
      ['localhost'],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb',
    },
  },
  output: 'standalone',
  // Disable ESLint and TypeScript checking during build for Docker
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
