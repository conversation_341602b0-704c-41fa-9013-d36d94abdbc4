# 🌐 Local Domain Setup Guide

## 📋 **Overview**

This guide explains how to set up local domains for testing the authentication system with proper cookie sharing between services.

## 🔧 **Why Local Domains?**

The authentication service sets cookies on specific domains. To test locally, we need to:
1. **Match the domain structure** of the production environment
2. **Enable cookie sharing** between frontend and backend
3. **Test authentication flow** without CORS issues

## 🛠️ **Setup Steps**

### 1. **Configure Local Domains**

Add these entries to your hosts file:

**Windows**: `C:\Windows\System32\drivers\etc\hosts`
**Mac/Linux**: `/etc/hosts`

```
127.0.0.1 ng-customer-local.dev.dev1.ngnair.com
127.0.0.1 ng-customer-fe-local.dev.dev1.ngnair.com
127.0.0.1 ng-customer-admin-local.dev.dev1.ngnair.com
```

### 2. **Environment Configuration**

The system uses these configurable domains:

| Service | Local Domain | Port |
|---------|-------------|------|
| Backend | `http://ng-customer-local.dev.dev1.ngnair.com:3060` | 3060 |
| Frontend (Embed) | `http://ng-customer-fe-local.dev.dev1.ngnair.com:3061` | 3061 |
| Admin | `http://ng-customer-admin-local.dev.dev1.ngnair.com:3062` | 3062 |

### 3. **Environment Variables**

#### Backend (.env.local)
```env
# Local Domain Configuration
LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062

# Auth Configuration
AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8

# CORS Configuration
CORS_ORIGINS=http://ng-customer-admin-local.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev1.ngnair.com:3061
```

#### Frontend (.env.local)
```env
# Local Domain Configuration
NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
NEXT_PUBLIC_LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
NEXT_PUBLIC_LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062

# Auth Configuration
NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8

# Debug Mode (prevents auto-redirect for debugging)
NEXT_PUBLIC_AUTH_DEBUG_MODE=true
```

## 🐛 **Debug Mode Features**

When `NEXT_PUBLIC_AUTH_DEBUG_MODE=true`, the admin panel shows:

- ✅ **Authentication Status** - Current auth state
- 🍪 **Cookie Information** - Access/refresh token presence
- 📋 **Debug Actions** - Console logging and manual login
- 🔐 **Manual Login Button** - Controlled redirect to auth service

## 🚀 **Testing Workflow**

1. **Start Services**:
   ```bash
   docker-compose up -d
   ```

2. **Access Admin Panel**:
   ```
   http://ng-customer-admin-local.dev1.ngnair.com:3062/
   ```

3. **Debug Authentication**:
   - Check cookie presence in debug panel
   - Use "Log cookies to console" to inspect values
   - Click "Go to Login" when ready to authenticate

4. **Test Backend API**:
   ```
   http://ng-customer-local.dev1.ngnair.com:3060/api
   ```

## 🔧 **Modular Configuration**

The domain configuration is **fully modular** and can be customized via environment variables:

```env
# Change these to match your environment
LOCAL_BACKEND_DOMAIN=http://your-backend-domain:3060
LOCAL_FRONTEND_DOMAIN=http://your-frontend-domain:3061
LOCAL_ADMIN_DOMAIN=http://your-admin-domain:3062
```

## 📁 **Copy-Paste Ready**

The auth folder includes all these configurations and can be copied to any project with:

1. ✅ **Configurable domains** via environment variables
2. ✅ **Debug mode** for authentication troubleshooting
3. ✅ **Production-ready** configuration
4. ✅ **CORS support** for local domains

## 🎯 **Next Steps**

1. **Set up hosts file** with local domains
2. **Configure environment variables** for your setup
3. **Start services** with docker-compose
4. **Test authentication flow** using debug mode
5. **Verify cookie sharing** between services

This setup enables **proper local testing** of the authentication system with real cookie sharing and domain-based authentication.
