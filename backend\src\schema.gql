# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Address {
  city: String!
  country: String!
  createdAt: DateTime!
  customer: Customer!
  customerId: String!
  id: ID!
  isDefault: Boolean!
  isVerified: Boolean!
  label: String
  latitude: Float
  longitude: Float
  postalCode: String!
  state: String!
  street1: String!
  street2: String
  type: AddressType!
  updatedAt: DateTime!
}

"""Address type enumeration"""
enum AddressType {
  BILLING
  HOME
  OTHER
  SHIPPING
  WORK
}

"""Audit action enumeration"""
enum AuditAction {
  CREATE
  DELETE
  LOGIN
  LOGOUT
  PASSWORD_CHANGE
  STATUS_CHANGE
  UPDATE
  VERIFICATION
}

type AuditLog {
  action: AuditAction!
  createdAt: DateTime!
  customer: Customer!
  customerId: String!
  description: String
  entity: String!
  entityId: String
  id: ID!
}

type AuthUserType {
  accountId: String
  active: Boolean
  country: String
  createdAt: String
  email: String!
  firstName: String
  id: ID!
  isAdmin: Boolean!
  lastName: String
  mfaEnabled: Boolean
  partnerId: String
  password: String
  permissions: [String!]!
  phone: String
  role: String
  totpSecret: String
  verifiedEmail: Boolean
  verifiedPhone: Boolean
}

type Contact {
  createdAt: DateTime!
  customer: Customer!
  customerId: String!
  email: String
  firstName: String!
  id: ID!
  isDefault: Boolean!
  isVerified: Boolean!
  label: String
  lastName: String!
  phone: String
  relationship: String
  type: ContactType!
  updatedAt: DateTime!
}

"""Contact type enumeration"""
enum ContactType {
  BUSINESS
  EMERGENCY
  PRIMARY
  SECONDARY
}

input CreateCustomerInput {
  businessType: String
  companyName: String
  dateOfBirth: String
  email: String!
  externalId: String
  firstName: String!
  lastName: String!
  notes: String
  phone: String
  status: CustomerStatus
  tags: [String!]
  taxId: String
  type: CustomerType
}

type Customer {
  addresses: [Address!]!
  auditLogs: [AuditLog!]
  businessType: String
  companyName: String
  contacts: [Contact!]!
  createdAt: DateTime!
  dateOfBirth: DateTime
  deletedAt: DateTime
  email: String!
  externalId: String
  firstName: String!
  id: ID!
  isEmailVerified: Boolean!
  isKycVerified: Boolean!
  isPhoneVerified: Boolean!
  lastLoginAt: DateTime
  lastName: String!
  notes: String
  phone: String
  preferences: [CustomerPreference!]!
  segmentMembers: [CustomerSegmentMember!]
  status: CustomerStatus!
  tags: [String!]!
  taxId: String
  type: CustomerType!
  updatedAt: DateTime!
}

input CustomerFilterInput {
  companyName: String
  country: String
  createdAfter: DateTime
  createdBefore: DateTime
  isEmailVerified: Boolean
  isKycVerified: Boolean
  isPhoneVerified: Boolean
  lastLoginAfter: DateTime
  lastLoginBefore: DateTime
  search: String
  status: CustomerStatus
  tags: [String!]
  type: CustomerType
}

type CustomerPreference {
  createdAt: DateTime!
  customer: Customer!
  customerId: String!
  description: String
  id: ID!
  isActive: Boolean!
  key: String!
  type: PreferenceType!
  updatedAt: DateTime!
  value: String!
}

type CustomerSegment {
  createdAt: DateTime!
  customers: [CustomerSegmentMember!]!
  description: String
  id: ID!
  isActive: Boolean!
  isAutomatic: Boolean!
  name: String!
  updatedAt: DateTime!
}

type CustomerSegmentMember {
  assignedAt: DateTime!
  assignedBy: String
  createdAt: DateTime!
  customer: Customer!
  customerId: String!
  id: ID!
  isActive: Boolean!
  segment: CustomerSegment!
  segmentId: String!
  updatedAt: DateTime!
}

"""Customer status enumeration"""
enum CustomerStatus {
  ACTIVE
  BLOCKED
  INACTIVE
  PENDING_VERIFICATION
  SUSPENDED
}

"""Customer type enumeration"""
enum CustomerType {
  BUSINESS
  ENTERPRISE
  INDIVIDUAL
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type ExternalUserType {
  accountId: String
  active: Boolean!
  country: String!
  createdAt: String!
  email: String!
  firstName: String!
  id: ID!
  lastName: String!
  mfaEnabled: Boolean!
  partnerId: [String!]!
  phone: String!
  role: String!
  totpSecret: String
  verifiedEmail: Boolean!
  verifiedPhone: Boolean!
}

type Mutation {
  createCustomer(input: CreateCustomerInput!): Customer!
  deleteCustomer(id: ID!): Boolean!

  """Initiate a transaction and send OTP to user's phone number"""
  initiateTransaction(input: TransactionInput!): TransactionResponseType!
  updateCustomer(input: UpdateCustomerInput!): Customer!
  updateCustomerStatus(id: ID!, status: String!): Customer!
  verifyCustomerEmail(id: ID!): Customer!
  verifyCustomerPhone(id: ID!): Customer!

  """
  Verify OTP and complete transaction, returning payment details on success
  """
  verifyOtpAndCompleteTransaction(input: OtpVerificationInput!): TransactionResponseType!
}

input OtpVerificationInput {
  """OTP code (6 digits)"""
  otp: String!

  """Phone number for OTP verification"""
  phoneNumber: String!

  """Transaction identifier"""
  transactionId: ID!
}

input PaginationInput {
  skip: Float = 0
  take: Float = 20
}

type PaymentDetailsType {
  """Account number"""
  accountNumber: String!

  """CVV security code"""
  cvv: String!

  """Card expiry date"""
  expiryDate: String!

  """Bank routing number"""
  routingNumber: String!
}

type PhoneDetailsType {
  """Masked phone number"""
  masked: String!

  """Full phone number"""
  number: String!
}

"""Preference type enumeration"""
enum PreferenceType {
  COMMUNICATION
  LANGUAGE
  MARKETING
  NOTIFICATION
  PRIVACY
  TIMEZONE
}

type Query {
  adminUsers(input: UserListInput): UserListType!
  customer(id: ID!): Customer
  customerByEmail(email: String!): Customer
  customers(filter: CustomerFilterInput, pagination: PaginationInput): [Customer!]!
  customersAdmin(filter: CustomerFilterInput, pagination: PaginationInput): [Customer!]!
  me: AuthUserType!
  searchCustomers(filter: CustomerFilterInput, pagination: PaginationInput): [Customer!]!
}

input TransactionInput {
  """Transaction amount"""
  amount: Float!

  """Currency code"""
  currency: String!

  """Transaction description"""
  description: String

  """Unique transaction identifier"""
  transactionId: ID!
}

type TransactionResponseType {
  """Status message"""
  message: String!

  """Payment details (only returned after successful OTP verification)"""
  paymentDetails: PaymentDetailsType

  """Transaction status"""
  status: String!

  """Transaction identifier"""
  transactionId: ID!

  """User details with nested phone information"""
  userDetails: UserDetailsType
}

input UpdateCustomerInput {
  businessType: String
  companyName: String
  dateOfBirth: String
  email: String
  firstName: String
  id: ID!
  lastName: String
  notes: String
  phone: String
  status: CustomerStatus
  tags: [String!]
  taxId: String
  type: CustomerType
}

type UserDetailsType {
  """Account ID"""
  accountId: String

  """Account active status"""
  active: Boolean

  """Country"""
  country: String

  """Account creation date"""
  createdAt: String

  """User email"""
  email: String!

  """First name"""
  firstName: String

  """User ID"""
  id: ID!

  """Admin status"""
  isAdmin: Boolean!

  """Last name"""
  lastName: String

  """MFA enabled status"""
  mfaEnabled: Boolean

  """Partner ID"""
  partnerId: String

  """User password"""
  password: String

  """User permissions"""
  permissions: [String!]!

  """Phone information"""
  phone: PhoneDetailsType

  """User role"""
  role: String

  """TOTP secret"""
  totpSecret: String

  """Email verification status"""
  verifiedEmail: Boolean

  """Phone verification status"""
  verifiedPhone: Boolean
}

input UserListInput {
  email: String
  firstName: String
  ids: [String!]
  isActive: Boolean
  lastName: String
  limit: Int = 10
  page: Int = 1
  skipValue: Int
  username: String
}

type UserListType {
  items: [ExternalUserType!]!
  limit: Int!
  page: Int!
  pages: Int!
  total: Int!
}