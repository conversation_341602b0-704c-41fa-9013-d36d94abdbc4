# === Stage 1: Builder ===
FROM node:20-alpine AS builder

WORKDIR /app

# Install deps only
COPY package.json package-lock.json ./
RUN npm install

# Copy full source
COPY . .

# Build arguments for environment variables (optional for build)
ARG NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY
ARG NEXT_PUBLIC_AUTH_FRONTEND_URL
ARG NEXT_PUBLIC_AUTH_JWKS_URL
ARG NEXT_PUBLIC_CUSTOMER_API_URL
ARG NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL

# Set environment variables for build (with fallbacks)
ENV NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=$NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY
ENV NEXT_PUBLIC_AUTH_FRONTEND_URL=$NEXT_PUBLIC_AUTH_FRONTEND_URL
ENV NEXT_PUBLIC_AUTH_JWKS_URL=$NEXT_PUBLIC_AUTH_JWKS_URL
ENV NEXT_PUBLIC_CUSTOMER_API_URL=$NEXT_PUBLIC_CUSTOMER_API_URL
ENV NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=$NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL

# Build the Next.js app
RUN npm run build

# === Stage 2: Production Image ===
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Copy the minimal production artifacts from the builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/tailwind.config.js ./tailwind.config.js
COPY --from=builder /app/postcss.config.js ./postcss.config.js

EXPOSE 3062

# Start the app
CMD ["npm", "start"]