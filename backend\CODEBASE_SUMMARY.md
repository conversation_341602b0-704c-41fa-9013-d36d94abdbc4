# Customer Microservice - Codebase Summary

## Overview

This is a **Customer Management Microservice** built with **NestJS** and **Fastify**, designed as part of a larger microservices architecture. The service provides comprehensive customer management capabilities including CRUD operations, verification workflows, transaction processing with OTP, and administrative functions.

## 🏗️ Architecture & Technology Stack

### Core Technologies
- **Framework**: NestJS with Fastify adapter (preferred over Express)
- **Database**: PostgreSQL with Prisma ORM
- **API Styles**: REST API + GraphQL
- **Authentication**: JWT-based with external auth service integration
- **Documentation**: Swagger/OpenAPI
- **Language**: TypeScript
- **Testing**: Jest
- **Containerization**: Docker with Docker Compose

### Key Features
- **Modular Architecture**: Organized into separate functional modules
- **Dual API Support**: Both REST and GraphQL endpoints
- **External Auth Integration**: Connects to external auth service at `ng-auth-dev.dev1.ngnair.com`
- **Transaction Processing**: OTP-based transaction verification with financial details
- **Comprehensive Logging**: Structured logging with Pino
- **Health Monitoring**: Multi-level health check endpoints
- **Database Migrations**: Prisma-based schema management

## 📁 Project Structure

```
src/
├── config/                 # Configuration management
├── modules/
│   ├── auth/              # Authentication & authorization
│   ├── customers-query/   # Customer search & read operations
│   ├── customers-management/ # Customer CRUD operations
│   ├── customers-verification/ # Email, phone, KYC verification
│   ├── customers-admin/   # Administrative customer operations
│   ├── customers-audit/   # Audit logging for customer actions
│   ├── customers-access-control/ # Access control & permissions
│   ├── customers-shared/  # Shared DTOs and utilities
│   ├── transactions/      # Transaction processing with OTP
│   ├── graphql/          # GraphQL configuration & resolvers
│   ├── health/           # Health check endpoints
│   └── shared/           # Shared guards and utilities
├── prisma/               # Database service
├── webhook/              # Generic webhook handling
├── main.ts              # Application bootstrap
└── app.module.ts        # Root module
```

## 🔐 Authentication System

### External Auth Service Integration
- **Service URL**: `https://ng-auth-dev.dev1.ngnair.com`
- **Method**: JWT token validation via `/auth/me` endpoint
- **User Types**: Admin users (`global_admin`) and regular users (`account_user`)
- **Permissions**: Role-based access control with scoped permissions

### Authentication Endpoints
- `GET /auth/me` - Get current user profile
- `GET /auth/admin/users` - Admin-only user listing with pagination

### Guards & Security
- **ApiGuard**: General authentication for all users
- **AdminGuard**: Admin-only access control
- **JWT Strategy**: Passport-based JWT validation

## 👥 Customer Management Modules

### 1. Customers Query Module (`customers-query`)
**Purpose**: Read operations, search, and filtering
- `GET /customers` - List customers with filtering & pagination
- `GET /customers/statistics` - Customer statistics
- `GET /customers/:id` - Get specific customer
- **Features**: Advanced filtering, search, pagination, statistics

### 2. Customers Management Module (`customers-management`)
**Purpose**: CRUD operations for customer records
- `POST /customers` - Create new customer
- `PUT /customers/:id` - Update customer
- `DELETE /customers/:id` - Delete customer
- **Features**: Email uniqueness validation, audit logging

### 3. Customers Verification Module (`customers-verification`)
**Purpose**: Verification workflows
- `POST /customers/:id/verify-email` - Mark email as verified
- `POST /customers/:id/verify-phone` - Mark phone as verified
- `POST /customers/:id/verify-kyc` - Mark KYC as verified
- **Features**: Audit trail for all verification actions

### 4. Customers Admin Module (`customers-admin`)
**Purpose**: Administrative operations (admin-only)
- `POST /customers/:id/activate` - Activate customer
- `POST /customers/:id/suspend` - Suspend customer
- `POST /customers/:id/block` - Block customer
- `PUT /admin/customers/:id` - Admin-level customer updates
- **Features**: Status management, admin-only access

### 5. Customers Audit Module (`customers-audit`)
**Purpose**: Audit logging and tracking
- Automatic logging of all customer-related actions
- Tracks: CREATE, UPDATE, DELETE, LOGIN, VERIFICATION, STATUS_CHANGE
- Stores old/new values, user context, IP addresses

## 💳 Transaction Processing Module

### Core Functionality
**Two-step transaction process with OTP verification**

### Endpoints
- `POST /transactions/initiate` - Start transaction and send OTP
- `POST /transactions/verify-otp` - Verify OTP and complete transaction

### Transaction Flow
1. **Initiation**: User submits transaction details
2. **OTP Generation**: System sends OTP to user's phone
3. **OTP Verification**: User submits OTP for verification
4. **Completion**: Upon successful OTP verification, returns:
   - Complete user profile with nested phone details
   - Financial payment details (routing number, CVV, account number)

### Security Features
- Phone number validation against user account
- Demo OTP: `123456` (for development)
- Masked phone numbers in logs
- Financial details only released after successful verification

## 🗃️ Database Schema

### Core Entities
- **Customer**: Main customer entity with personal/business info
- **Address**: Customer addresses (home, work, billing, shipping)
- **Contact**: Additional contact information
- **CustomerPreference**: User preferences and settings
- **AuditLog**: Complete audit trail
- **CustomerSegment**: Customer grouping and segmentation

### Key Features
- **Soft Deletes**: `deletedAt` field for safe deletion
- **Audit Trail**: Comprehensive logging of all changes
- **Flexible Addressing**: Multiple address types per customer
- **Verification Tracking**: Email, phone, and KYC verification status
- **Business Support**: Fields for business customers (tax ID, company name)

## 🔍 API Documentation & Testing

### Swagger Documentation
- **URL**: `http://localhost:3001/api`
- **Features**: Interactive API documentation with authentication
- **Organization**: Tagged by module (customers-query, customers-management, etc.)

### GraphQL Playground
- **URL**: `http://localhost:3001/graphql`
- **Features**: Full GraphQL schema with mutations and queries
- **Parallel Implementation**: All REST endpoints also available via GraphQL

## 🏥 Health & Monitoring

### Health Check Endpoints
- `GET /health/public` - Public health check (no auth required)
- `GET /health/user` - User-authenticated health check
- `GET /health/admin` - Admin-only health check with detailed system status
- `GET /webhooks/health` - Webhook service health check

### Monitoring Features
- **Structured Logging**: Pino-based logging with request tracking
- **Error Handling**: Comprehensive error responses
- **Request Tracing**: User context in all operations

## 🔗 External Integrations

### Auth Service
- **URL**: `https://ng-auth-dev.dev1.ngnair.com`
- **Purpose**: User authentication and authorization
- **Integration**: JWT token validation and user profile retrieval

### Webhook Support
- `POST /webhooks` - Generic webhook endpoint for external integrations
- **Features**: Flexible payload handling, error logging

## 🚀 Development & Deployment

### Environment Configuration
- **Port**: 3001 (configurable)
- **Database**: PostgreSQL with connection pooling
- **CORS**: Configurable allowed origins
- **JWT**: Configurable secret and expiration

### Docker Support
- **Multi-stage build** with Node.js 20+
- **PostgreSQL 15** database container
- **Volume persistence** for database data
- **Health checks** and proper container orchestration

### Scripts & Commands
- `npm run dev` - Development with hot reload
- `npm run build` - Production build with Prisma generation
- `npm run test` - Jest testing suite
- `npm run migrate:up` - Database migrations
- `npm run seed:*` - Database seeding scripts

## 🎯 Key Design Patterns

### Modular Architecture
- **Separation of Concerns**: Each module handles specific functionality
- **Shared Resources**: Common DTOs, guards, and utilities
- **Clean Dependencies**: Proper module imports and exports

### Security First
- **Authentication Required**: All endpoints require valid JWT
- **Role-Based Access**: Admin vs user permissions
- **Audit Everything**: Complete audit trail for compliance

### API Consistency
- **Standardized Responses**: Consistent DTO patterns
- **Error Handling**: Uniform error response format
- **Documentation**: Comprehensive Swagger annotations

This microservice represents a production-ready customer management system with enterprise-grade features including security, audit trails, transaction processing, and comprehensive API documentation.
