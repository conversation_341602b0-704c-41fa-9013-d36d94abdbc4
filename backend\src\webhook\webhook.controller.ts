import { 
  <PERSON>, 
  Post, 
  Body, 
  Logger, 
  HttpStatus,
  HttpException,
  Get
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { WebhookService } from "./webhook.service";

@ApiTags("webhooks")
@Controller("webhooks")
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly webhookService: WebhookService) {}

  @Post()
  @ApiOperation({ summary: "Handle generic webhook" })
  @ApiBody({
    schema: {
      type: "object"
    }
  })
  @ApiResponse({
    status: 200,
    description: "Webhook processed successfully"
  })
  async handleWebhook(
    @Body() payload: Record<string, unknown>,
  ): Promise<Record<string, unknown>> {
    try {
      this.logger.log(`Received webhook: ${JSON.stringify(payload)}`);
      return await this.webhookService.processWebhook(payload);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error handling webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error handling webhook");
      }
      throw new HttpException("Webhook processing failed", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get("health")
  @ApiOperation({ summary: "Health check for webhook endpoint" })
  @ApiResponse({
    status: 200,
    description: "Webhook service is healthy",
    schema: {
      properties: {
        status: { type: "string", example: "ok" }
      }
    }
  })
  healthCheck() {
    return { status: "ok" };
  }
}