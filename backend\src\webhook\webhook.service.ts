import { Injectable, Logger } from "@nestjs/common";

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  /**
   * Generic webhook handling method
   */
  processWebhook(payload: Record<string, unknown>): Record<string, unknown> {
    try {
      this.logger.log(`Processing webhook: ${JSON.stringify(payload)}`);
      return {
        status: "success",
        message: "Webhook processed",
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error processing webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error processing webhook");
      }
      throw error;
    }
  }
}